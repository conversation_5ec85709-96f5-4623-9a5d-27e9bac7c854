group 'io.flutter.plugins.webview_cookie_manager'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://storage.googleapis.com/download.flutter.io" }
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'io.flutter.plugins.webview_cookie_manager'
    compileSdkVersion 35

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 35
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    testImplementation 'junit:junit:4.13.1'
}
