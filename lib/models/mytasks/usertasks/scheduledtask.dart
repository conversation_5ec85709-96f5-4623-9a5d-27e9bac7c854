class ScheduledTaskRecords {
  int? milestoneId;
  String? milestoneName;
  String? description;
  String? baseLineStart;
  String? baseLineEnd;
  String? forecastStart;
  String? forecastEnd;
  String? actualStart;
  String? actualFinish;
  String? resourceUserName;
  String? resourceRole;
  String? entityType;
  String? entityTypeValue;
  int? entityId;
  String? entityName;
  String? transType;
  String? programName;
  String? statusType;

  ScheduledTaskRecords({
    this.milestoneId,
    this.milestoneName,
    this.description,
    this.baseLineStart,
    this.baseLineEnd,
    this.forecastStart,
    this.forecastEnd,
    this.actualStart,
    this.actualFinish,
    this.resourceUserName,
    this.resourceRole,
    this.entityType,
    this.entityTypeValue,
    this.entityId,
    this.entityName,
    this.transType,
    this.programName,
    this.statusType,
  });
  ScheduledTaskRecords.fromJson(Map<String, dynamic> json) {
    milestoneId = json['milestone_id']?.toInt();
    milestoneName = json['milestone_name']?.toString();
    description = json['description']?.toString();
    baseLineStart = json['base_line_start']?.toString();
    baseLineEnd = json['base_line_end']?.toString();
    forecastStart = json['forecast_start']?.toString();
    forecastEnd = json['forecast_end']?.toString();
    actualStart = json['actual_start']?.toString();
    actualFinish = json['actual_finish']?.toString();
    resourceUserName = json['resource_user_name']?.toString();
    resourceRole = json['resource_role']?.toString();
    entityType = json['entity_type']?.toString();
    entityTypeValue = json['entity_type_value']?.toString();
    entityId = json['entity_id']?.toInt();
    entityName = json['entity_name']?.toString();
    transType = json['trans_type']?.toString();
    programName = json['program_name']?.toString();
    statusType = json['status_type']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['milestone_id'] = milestoneId;
    data['milestone_name'] = milestoneName;
    data['description'] = description;
    data['base_line_start'] = baseLineStart;
    data['base_line_end'] = baseLineEnd;
    data['forecast_start'] = forecastStart;
    data['forecast_end'] = forecastEnd;
    data['actual_start'] = actualStart;
    data['actual_finish'] = actualFinish;
    data['resource_user_name'] = resourceUserName;
    data['resource_role'] = resourceRole;
    data['entity_type'] = entityType;
    data['entity_type_value'] = entityTypeValue;
    data['entity_id'] = entityId;
    data['entity_name'] = entityName;
    data['trans_type'] = transType;
    data['program_name'] = programName;
    data['status_type'] = statusType;
    return data;
  }
}

class ScheduledTask {
  List<ScheduledTaskRecords?>? records;

  ScheduledTask({
    this.records,
  });
  ScheduledTask.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      final v = json['records'];
      final arr0 = <ScheduledTaskRecords>[];
      v.forEach((v) {
        arr0.add(ScheduledTaskRecords.fromJson(v));
      });
      records = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (records != null) {
      final v = records;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['records'] = arr0;
    }
    return data;
  }
}
