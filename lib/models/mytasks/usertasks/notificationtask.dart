class NotificationTaskRecords {
  int? notificationId;
  String? entityType;
  int? entityId;
  String? message;
  String? assignedTo;
  String? completionDate;
  String? entityNumber;
  String? entityName;
  String? creationDate;

  NotificationTaskRecords({
    this.notificationId,
    this.entityType,
    this.entityId,
    this.message,
    this.assignedTo,
    this.completionDate,
    this.entityNumber,
    this.entityName,
    this.creationDate,
  });
  NotificationTaskRecords.fromJson(Map<String, dynamic> json) {
    notificationId = json['notification_id']?.toInt();
    entityType = json['entity_type']?.toString();
    entityId = json['entity_id']?.toInt();
    message = json['message']?.toString();
    assignedTo = json['assigned_to']?.toString();
    completionDate = json['completion_date']?.toString();
    entityNumber = json['entity_number']?.toString();
    entityName = json['entity_name']?.toString();
    creationDate = json['creation_date']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['notification_id'] = notificationId;
    data['entity_type'] = entityType;
    data['entity_id'] = entityId;
    data['message'] = message;
    data['assigned_to'] = assignedTo;
    data['completion_date'] = completionDate;
    data['entity_number'] = entityNumber;
    data['entity_name'] = entityName;
    data['creation_date'] = creationDate;
    return data;
  }
}

class NotificationTask {
  List<NotificationTaskRecords?>? records;

  NotificationTask({
    this.records,
  });
  NotificationTask.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      final v = json['records'];
      final arr0 = <NotificationTaskRecords>[];
      v.forEach((v) {
        arr0.add(NotificationTaskRecords.fromJson(v));
      });
      records = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (records != null) {
      final v = records;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['records'] = arr0;
    }
    return data;
  }
}
