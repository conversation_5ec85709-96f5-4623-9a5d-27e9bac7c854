import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get_instance/src/extension_instance.dart';
import 'package:get/utils.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/common/widgets/components.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/projectsearch_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import 'package:tangoworkplace/screens/projectmanagement/program/program_search_screen.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/project_details_screen.dart';
import 'package:tangoworkplace/utils/common_utils.dart';
import 'package:geolocator/geolocator.dart';

import '../../../common/component_utils.dart';
import '../../../models/ta_admin/entitystatus.dart';
import '../../common/utils/entityerrorpg.dart';
import '../../mytasks/workflow/userpendingtasks_screen.dart';
import 'search/project_filter_widget.dart';

class ProjectSearch extends StatelessWidget {
  static const routName = '/ProjectSearch';
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String? _projectFlag;
  // TextEditingController _searchController = TextEditingController();
  ProjectSearchController projectSearchState = Get.put(ProjectSearchController());

  ProjectSearchController projectSearchCtrlr = Get.find<ProjectSearchController>();
  LabelController talabel = Get.find<LabelController>();

  ProjectSearch({super.key});

  Future _refreshProjects() async {
    projectSearchCtrlr.fetchProjects(searchText: projectSearchCtrlr.searchCtrl.text);
  }

  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;
    Position currentPosition;

    // Test if location services are enabled.
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        debugPrint('-------------Location permissions are denied-------------------');
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      debugPrint('------------- denied forever-------------------');
      return Future.error('Location permissions are permanently denied, we cannot request permissions.');
    }

    debugPrint('------------- success-------------------');
    return Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.best, forceAndroidLocationManager: true)
        .then((Position position) {
      currentPosition = position;
    }).catchError((e) {
      debugPrint(e);
    }) as Future<Position>;
  }

  _getCurrentLocation() {
    Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.best, forceAndroidLocationManager: true)
        .then((Position position) {})
        .catchError((e) {
      debugPrint(e);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Obx(
          () => Text(talabel.get('TMCMOBILE_HOME_PROJECTS')!.value!,
              style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
        ),
        // actions: [
        //   IconButton(
        //     color: ComponentUtils.primecolor,
        //     icon: const Icon(Icons.my_location),
        //     onPressed: () async {
        //       Position p = await _determinePosition();
        //       debugPrint('latitude     ${p.latitude}');
        //     },
        //   ),
        // ],
        backgroundColor: Colors.white,
        elevation: 5,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () {
            //Get.off(() => HomeScreen());
            Get.back();
          },
        ),
        actions: [
          IconButton(
              onPressed: () async {
                Get.to(() => ProjectFilterWidget());
                projectSearchCtrlr.filterwidgetloading.value = true;
                await talabel.getlabels('TMCMOBILE_PROJECTSEARCH', 'Project_search', filtertype: 'tab');
                Future.delayed(const Duration(seconds: 1), () async {
                  projectSearchCtrlr.filterwidgetloading.value = false;
                });
              },
              icon: Icon(
                Icons.search,
                color: ComponentUtils.primecolor,
              )),
          Obx(
            () => talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION') != null
                ? IconButton(
                    onPressed: () {
                      navigationBottomSheet(context);
                    },
                    icon: Icon(
                      Icons.apps,
                      color: ComponentUtils.primecolor,
                    ))
                : Container(),
          ),
        ],
      ),
      key: _scaffoldKey,
      body: Column(
        children: <Widget>[
          filterChipContainer(),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshProjects,
              child: GetX<ProjectSearchController>(
                initState: (state) async {
                  projectSearchCtrlr.isLoading(true);
                  await talabel.getlabels('TMCMOBILE_PROJECTSEARCH', 'Project_search', filtertype: 'tab');
                  await projectSearchCtrlr.fetchProjects(action: 'onload');
                  // projectSearchCtrlr.statuslov.value = await CommonServices.getStatuses('PROJECT');
                },
                builder: (_) {
                  return _.isLoading.isTrue
                      ? const ProgressIndicatorCust()
                      : _.projects.isEmpty
                          ? Center(
                              child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                          : ListView.builder(
                              itemBuilder: (context, index) {
                                return listitem(context, _.projects[index]);
                              },
                              itemCount: _.projects.length,
                            );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget filterlov() {
    return Obx(
      () => PopupMenuButton(
        onSelected: (dynamic value) {
          debugPrint('selected   $value');
          projectSearchCtrlr.allmyfilter.value = value;
          projectSearchCtrlr.fetchProjects(searchText: projectSearchCtrlr.searchCtrl.text.toString());
        },
        initialValue: projectSearchCtrlr.allmyfilter.value,
        icon: const Icon(Icons.more_vert, color: Colors.black),
        //color: ComponentUtils.primecolor,
        itemBuilder: (context) => [
          PopupMenuItem(
            value: 'My',
            child: Text("My", style: TextStyle(fontSize: 12, color: ComponentUtils.primecolor)),
          ),
          PopupMenuItem(
            value: 'All',
            child: Text("All", style: TextStyle(fontSize: 12, color: ComponentUtils.primecolor)),
          ),
        ],
      ),
    );
  }

  Widget listitem(BuildContext context, ProjectsView p) {
    const primary = Color(0xff696b9e);
    const secondary = Color(0xfff29a94);

    return GestureDetector(
        onTap: () async {
          String mode = await projectSearchCtrlr.getUserEntityEditAccess(
              entityid: p.projectId.toString(), entitytype: 'PROJECT', roFlag: p.readOnlyFlag);

          if (mode == 'error') {
            Get.to(
              () => EntityErrorPg(
                entitytype: 'Project',
                entityid: p.projectId,
                entityname: p.projectName,
              ),
            );
          } else {
            Get.to(
                () => ProjectDetails(
                      proj: p,
                      parent_mode: mode,
                      tabroles: projectSearchCtrlr.tabroles.value,
                    ),
                arguments: [
                  {"projectId": p.projectId},
                  {"porojectview": p}
                ]);
          }
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.white,
          ),
          width: double.infinity,
          //height: 110,
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${p.projectName}',
                      style: TextStyle(color: primary, fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.shield,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(p.projectNumber!, style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                        // Row(
                        //   children: <Widget>[
                        //     Icon(
                        //       Icons.bolt,
                        //       color: secondary,
                        //       size: 15,
                        //     ),
                        //     SizedBox(
                        //       width: 5,
                        //     ),
                        Text(p.statusDesc ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        //   ],
                        // ),
                      ],
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Row(children: [
                          Icon(
                            Icons.assistant_photo,
                            color: secondary,
                            size: 15,
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          Text(p.entityType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                        ]),
                        Text(p.ownershipType ?? '', style: TextStyle(color: primary, fontSize: 12, letterSpacing: .3)),
                      ],
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Row(
                      children: <Widget>[
                        Icon(
                          Icons.location_on,
                          color: secondary,
                          size: 15,
                        ),
                        const SizedBox(
                          width: 5,
                        ),
                        Expanded(
                          child: Text((p.entityAddress ?? '') + ' ' + (p.city ?? '') + ' ' + (p.state ?? ' '),
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: primary,
                                fontSize: 12,
                                letterSpacing: .1,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget filterChipContainer() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: Get.width, // Adjust this value as needed
            child: Container(
              padding: const EdgeInsets.only(left: 12, right: 12, top: 5, bottom: 2),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Wrap(
                  alignment: WrapAlignment.start,
                  spacing: 6.0,
                  runSpacing: 6.0,
                  children: List<Widget>.generate(
                    projectSearchCtrlr.filterList.length,
                    (int index) {
                      return projectSearchCtrlr.filterList[index];
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void navigationBottomSheet(context) {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext bc) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Wrap(
              // spacing: 60,
              children: <Widget>[
                Container(height: 10),
                Container(
                    padding: const EdgeInsets.fromLTRB(10, 10, 5, 10),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10.0),
                        boxShadow: const [BoxShadow(color: Colors.grey, blurRadius: 5, offset: Offset(0, 0))]),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION')?.value ?? 'Navigation',
                          style: const TextStyle(
                            // color: Colors.grey,
                            fontSize: 15, //isLargeScreen ? 25 : 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        GridView.count(
                            physics: const NeverScrollableScrollPhysics(),
                            crossAxisCount: 3,
                            childAspectRatio: 1.0,
                            //padding: const EdgeInsets.all(10.0),
                            padding: const EdgeInsets.only(left: 24, right: 24, bottom: 10, top: 10),
                            mainAxisSpacing: 20.0,
                            crossAxisSpacing: 20.0,
                            shrinkWrap: true,
                            children: <Widget>[
                              if (talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_CREATEPROJECT') != null)
                                navTile(
                                    'lib/icons/project/create_project.png',
                                    talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_CREATEPROJECT')?.value ??
                                        'Create Project'),
                              if (talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_PROGRAMS') != null)
                                GestureDetector(
                                  child: navTile('lib/icons/programs.png',
                                      talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_PROGRAMS')?.value ?? 'Programs'),
                                  onTap: () {
                                    Get.back();
                                    Get.to(ProgramSearch());
                                  },
                                ),
                              if (talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_SUPPLIERS') != null)
                                navTile('lib/icons/project/suppliers.png',
                                    talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_SUPPLIERS')?.value ?? 'Suppliers'),
                              if (talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_HIGHPROFILE') != null)
                                navTile(
                                    'lib/icons/project/high_profile_projects.png',
                                    talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_HIGHPROFILE')?.value ??
                                        'High Profile'),
                              if (talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_CONFIDENTIALITY') != null)
                                navTile(
                                    'lib/icons/project/confidential.png',
                                    talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_CONFIDENTIALITY')?.value ??
                                        'Configential'),
                              if (talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_MYAPPROVALS') != null)
                                GestureDetector(
                                  child: navTile(
                                      'lib/icons/approvals.png',
                                      talabel.get('TMCMOBILE_PROJECTSEARCH_NAVIGATION_MYAPPROVALS')?.value ??
                                          'My Approvals'),
                                  onTap: () {
                                    Get.back();
                                    Get.to(Myapprovals(navFrom: 'project'));
                                  },
                                ),
                            ]),
                      ],
                    )),
                //Container(height: 10),
                // const Divider(
                //   color: Colors.grey,
                //   thickness: 1,
                //   // endIndent: 10,
                //   //indent: 10,
                // ),
                // GestureDetector(
                //   child: const Text(
                //     "My Approvals",
                //     style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                //   ),
                //   onTap: () {
                //     Get.back();
                //     Get.to(Myapprovals(navFrom: 'project'));
                //   },
                // ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: <Widget>[
                    TextButton(
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.transparent,
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text("Close",
                          style: TextStyle(color: Theme.of(context).colorScheme.primary)), // Add the button text.
                    ),

                    // ElevatedButton(
                    //   style: ElevatedButton.styleFrom(
                    //     backgroundColor: Theme.of(context).colorScheme.primary,
                    //   ),
                    //   onPressed: () {},
                    //   child:
                    //       Text("Read More", style: TextStyle(color: Theme.of(context).colorScheme.inversePrimary)), // Add the button text.
                    // )
                  ],
                ),
                const SizedBox(height: 60),
              ],
            ),
          );
        });
  }

  Widget navTile(String imgpath, String title) {
    return Container(
      padding: const EdgeInsets.only(top: 10),
      //color: Colors.amber,
      child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: <Widget>[
        Image(
          image: AssetImage(imgpath),
          width: 45,
          height: 45,
        ),
        const SizedBox(
          height: 5,
        ),
        Text(title, style: TextStyle(color: ComponentUtils.primary, fontSize: 12, letterSpacing: .3)),
      ]),
    );
  }
}
