import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/common/photo.dart';
import 'package:tangoworkplace/models/project/projectview.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/common/adhoctasks_controller.dart';
import 'package:tangoworkplace/providers/common/documents_controller.dart';
import 'package:tangoworkplace/providers/common/documents_tree_controller.dart';
import 'package:tangoworkplace/providers/common/entitycomments_controller.dart';
import 'package:tangoworkplace/providers/common/entitymilestones_controller.dart';
import 'package:tangoworkplace/providers/common/photos_controller.dart';
import 'package:tangoworkplace/providers/common/punchlist_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/budget_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/budget/budget_summary_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/projectdeatils_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/projectgeneral_controller.dart';
import 'package:tangoworkplace/providers/projectmanagement/project/statusreport/statusreport_controller.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/screens/common/adhoctasks/entity_adhoctasks.dart';
import 'package:tangoworkplace/screens/common/documents/documents.dart';
import 'package:tangoworkplace/screens/common/comments/entity_comments.dart';
import 'package:tangoworkplace/screens/common/meeting_minutes/meeting_minutes_screen.dart';
import 'package:tangoworkplace/screens/common/milestone/entity_milestones.dart';
import 'package:tangoworkplace/screens/common/photos/photos.dart';
import 'package:tangoworkplace/screens/common/photos/photosgrid.dart';
import 'package:tangoworkplace/screens/common/punchlist/entity_punchlist.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/budget/budget_overview.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/statusreport/statusreport_screen.dart';
import 'package:tangoworkplace/screens/projectmanagement/project/tabs/project_general_tab.dart';

import '../../../common/component_utils.dart';
import '../../../providers/common/commoncontacts_controller.dart';
import '../../../providers/common/meetingminute/meetingminutes_controller.dart';
import '../../../providers/common/photosgrid_controller.dart';
import '../../common/contacts/contacts_data.dart';

class ProjectDetails extends StatelessWidget {
  final ProjectsView? proj;
  String? parent_mode;
  Map? tabroles;
  ProjectDetails({super.key, this.proj, this.parent_mode, this.tabroles});

  static const routName = '/ProjectDetails';
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final projecttabs = <Tab>[];
  Applabel? label;
  LabelController talabel = Get.find<LabelController>();

  var generaltab;
  var scheduletab;
  var budgettab;
  var photostab;
  var docstab;
  var parentdoctab;
  var punchlisttab;
  var commentstab;
  var adhoctasktab;
  var statusreporttab;
  var meetingminutestab;
  var contactstab;
  List<Tab> tabsdata = [];

  String? _setTablabels(Applabel? label, String role) {
    if (label != null && tabrolecheck(role)) {
      projecttabs.add(Tab(
        text: label.value,
      ));
      return label.value;
    }
    return null;
  }

  bool tabrolecheck(String tabrole) {
    return tabroles!.containsKey(tabrole) && tabroles![tabrole] != 'na';
  }

  Future _getlabels(LabelController labCtrl) async {
    try {
      // labCtrl.setdataloading.value = true;
      await labCtrl.getlabels('TMCMOBILE_PROJECTS', 'project_details', filtertype: 'page');

      Future.delayed(const Duration(seconds: 0), () {
        generaltab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_GENERAL'), 'general');
        scheduletab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_SCHEDULE'), 'schedule');
        budgettab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_BUDGET'), 'budget');
        photostab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_PHOTOS'), 'photos');
        docstab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_DOCUMENTS'), 'document');
        parentdoctab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_PARENTDOCUMENTS'), 'document');
        punchlisttab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_PUNCHLIST'), 'punchlist');
        commentstab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_COMMENTS'), 'comments');
        adhoctasktab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_ADHOCTASKS'), 'adhoctasks');
        statusreporttab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_STATUSREPORTS'), 'statusreport');
        meetingminutestab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_MEETINGMINUTES'), 'meetingminutes');
        contactstab = _setTablabels(labCtrl.get('TMCMOBILE_PROJECTS_CONTACTS'), 'contacts');
        debugPrint('projecttabs-------$projecttabs');
        debugPrint('tabroles-------$tabroles');
        debugPrint('generaltab-------$generaltab');
        debugPrint('scheduletab-------$scheduletab');
        debugPrint('budgettab-------$budgettab');
        debugPrint('photostab-------$photostab');
        debugPrint('docstab-------$docstab');
        debugPrint('parentdoctab-------$parentdoctab');
        debugPrint('punchlisttab-------$punchlisttab');
        debugPrint('commentstab-------$commentstab');
        debugPrint('adhoctasktab-------$adhoctasktab');
        debugPrint('statusreporttab-------$statusreporttab');
        debugPrint('meetingminutestab-------$meetingminutestab');
        debugPrint('contactstab-------$contactstab');
      });
    } catch (e) {
      debugPrint('$e');
    }
    labCtrl.setdataloading.value = false;
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('projectname-------${proj!.projectName}');
    debugPrint('projectname-------$tabroles');
    talabel.setdataloading.value = true;

    ProjectGeneralController projectGeneralController = Get.put(ProjectGeneralController());
    ProjectDetailsController projectDetailsState = Get.put(ProjectDetailsController());
    EntityCommentsController entityCommentsController = Get.put(EntityCommentsController());
    PhotosGridController photosController = Get.put(PhotosGridController());
    EntityMilestonesController entityMilestonesController = Get.put(EntityMilestonesController());
    DocumentController documentController = Get.put(DocumentController());
    DocumentsTreeController documentTreeController = Get.put(DocumentsTreeController());
    EntityPunchlistController entityPunchlistController = Get.put(EntityPunchlistController());
    BudgetController budgetController = Get.put(BudgetController());
    BudgetSummaryController budgetSummaryController = Get.put(BudgetSummaryController());
    EntityAdhicTasksController adhoctaskController = Get.put(EntityAdhicTasksController());
    StatusReportController statusrepoCtrl = Get.put(StatusReportController());
    MeetingMinutesController meetingminutesCtrl = Get.put(MeetingMinutesController());
    CommonContactsController contactCtrl = Get.put(CommonContactsController());

    return Scaffold(
      appBar: AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        title: Text(proj!.projectName!,
            style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
            ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: ComponentUtils.backpageIcon,
          color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
          onPressed: () async {
            debugPrint('------back-------');
            Get.back();
            await talabel.getlabels('TMCMOBILE_PROJECTSEARCH', 'Project_search', filtertype: 'tab');
          },
        ),
      ),
      key: _scaffoldKey,
      body: GetX<LabelController>(initState: (state) {
        Future.delayed(Duration.zero, () async {
          await _getlabels(talabel);
          //debugPrint('root folder id ------${proj.parentRootFolderId}');
        });
      }, builder: (_) {
        return (talabel.setdataloading.value)
            ? const ProgressIndicatorCust()
            : DefaultTabController(
                initialIndex: 0,
                length: (projecttabs.isNotEmpty) ? projecttabs.length : 1,
                child: Column(children: <Widget>[
                  Material(
                    elevation: 5,
                    color: Colors.white,
                    child: TabBar(
                      //controller: _tabController,
                      isScrollable: true,
                      labelColor: ComponentUtils.tablabelcolor,
                      unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
                      indicatorColor: ComponentUtils.tabindicatorColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelStyle: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.normal,
                          fontSize: 14,
                          color: ComponentUtils.tablabelcolor),
                      tabs: (projecttabs.isNotEmpty)
                          ? projecttabs
                          : [
                              const Tab(
                                text: 'General',
                              )
                            ],
                    ),
                  ),
                  Expanded(
                    child: (projecttabs.isEmpty)
                        ? TabBarView(children: [ProjectGeneral(project: proj)])
                        : TabBarView(
                            children: [
                              if (generaltab != null) ProjectGeneral(project: proj),
                              if (scheduletab != null)
                                EntityMilestones(
                                  entityType: 'PROJECT',
                                  entityId: proj!.projectId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['schedule'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (budgettab != null)
                                BudgetOverview(
                                  entityType: 'PROJECT',
                                  entityId: proj!.projectId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['budget'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (photostab != null)
                                PhotosGrid(
                                  //Photos(
                                  entityType: 'PROJECT',
                                  entityId: proj!.projectId,

                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['photos'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (docstab != null)
                                // DocumentsTree(
                                //   rootFolderId: proj!.rootFolderId,
                                //   entityType: 'PROJECT',
                                //   entityId: proj!.projectId,
                                //   mode:
                                //       (parent_mode == 'edit' && tabroles != null && tabroles!['document'] == 'edit') ? 'edit' : 'view',
                                // ),
                                Documents(
                                  rootfolderid: proj!.rootFolderId,
                                  entityType: 'PROJECT',
                                  entityId: proj!.projectId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['document'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (parentdoctab != null)
                                Documents(
                                  rootfolderid: proj!.parentRootFolderId,
                                  entityType: proj!.entityType,
                                  entityId: proj!.projectId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['document'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (punchlisttab != null)
                                EntityPunchlist(
                                  entityType: 'PUNCHLIST',
                                  entityId: proj!.projectId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['punchlist'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (commentstab != null)
                                EntityComments(
                                  entityType: 'PROJECT',
                                  entityId: proj!.projectId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['comments'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (adhoctasktab != null)
                                EntityAdhocTasks(
                                  entityType: 'PROJECT',
                                  entityId: proj!.projectId,
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['adhoctasks'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (statusreporttab != null)
                                StatusReports(
                                  entityid: proj!.projectId,
                                  projectview: proj,
                                  mode:
                                      (parent_mode == 'edit' && tabroles != null && tabroles!['statusreport'] == 'edit')
                                          ? 'edit'
                                          : 'view',
                                ),
                              if (meetingminutestab != null)
                                MeetingMinutes(
                                  entityid: proj!.projectId,
                                  entitytype: 'PROJECT',
                                  projectview: proj,
                                  mode: (parent_mode == 'edit' &&
                                          tabroles != null &&
                                          tabroles!['meetingminutes'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                              if (contactstab != null)
                                ContactsData(
                                  entityId: proj!.projectId,
                                  entityType: 'PROJECT',
                                  mode: (parent_mode == 'edit' && tabroles != null && tabroles!['contacts'] == 'edit')
                                      ? 'edit'
                                      : 'view',
                                ),
                            ],
                          ),
                  ),
                ]),
              );
      }),
    );
  }
}
