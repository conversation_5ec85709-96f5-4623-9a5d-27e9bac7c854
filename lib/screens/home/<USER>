import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/common_import.dart';
import 'package:tangoworkplace/models/ta_admin/app_label.dart';
import 'package:tangoworkplace/providers/ta_admin/home_controller.dart';
import 'package:tangoworkplace/screens/agilequest/aq_loginscreen.dart';
import 'package:tangoworkplace/screens/agilequest/floor_plan/aq_floor_plan.dart';
import 'package:tangoworkplace/screens/home/<USER>';
import 'package:tangoworkplace/screens/mytasks/usertasks/mytasks_screen.dart';
import 'package:tangoworkplace/screens/sitemanagement/site/site_search.dart';
import 'package:tangoworkplace/screens/sitemanagement/target/target_search.dart';
import 'package:tangoworkplace/screens/space/buildings/buildings_search.dart';
import 'package:tangoworkplace/screens/space/find/find_screen.dart';
import 'package:tangoworkplace/screens/space/property/property_search.dart';
import 'package:tangoworkplace/screens/ta_admin/change_context_screen.dart';
import 'package:tangoworkplace/utils/widgetParser/widget_parser_engine.dart';
import '../../common/component_utils.dart';
import '../../providers/agilequest/aqhome_controller.dart';
import '../../providers/ta_admin/label_controller.dart';
import '../../utils/widgetParser/dynamic_widget_page.dart';
import '../../utils/widgetParser/widget_parser_controller.dart';
import '../contracts/lease/lease_home_screen.dart';
import '../locationmangement/locationentity/locationentity_search.dart';
import '../maintenance/servicerequest/servicerequest_search.dart';
import '../projectmanagement/program/program_search_screen.dart';
import '../sitemanagement/locate/locate.dart';
import '../space/reservations/reservations_screen.dart';
import '../space/reservations/reservations_search_screen.dart';
import '../mytasks/workflow/userpendingtasks_screen.dart';
import '../projectmanagement/project/project_search_screen.dart';
import '../space/find/find_search_screen.dart';
import 'package:badges/badges.dart' as badges;

import '../suppliermanagement/suppliermanagement_search.dart';

class HomeScreen extends StatefulWidget {
  static const routName = '/home';

  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  PageController? _pageController;
  int currentIndex = 0;
  static const _kDuration = Duration(milliseconds: 300);
  static const _kCurve = Curves.ease;
  var user;
  final _isInit = true;
  LabelController talabel = Get.find<LabelController>();
  HomeController homeController = Get.put(HomeController());
  HomeController homeCtrl = Get.find<HomeController>();
  AqHomeController aqhState = Get.put(AqHomeController());
  var hostenv;
  late double sWidth;
  get swidth => null;

  Future<void>? loadMetadata() {
    if (talabel.init.value) {
      Future.delayed(const Duration(seconds: 1), () async {
        homeCtrl.env.value = await SharedPrefUtils.readPrefStr(ConstHelper.hostNameVar);

        await homeCtrl.getUserInfo();
        await talabel.getlabels('TMCMOBILE_HOME', 'home', filtertype: 'page');
        homeCtrl.aqserverurl.value = await SharedPrefUtils.readPrefStr(ConstHelper.aqbaseurl);
        debugPrint('aq base url >>>>>${homeCtrl.aqserverurl.value}');
      });
      talabel.init.value = false;
    }
    return null;
  }

  Future<void>? loadPendingTasksdata() {
    Future.delayed(const Duration(seconds: 0), () async {
      await homeCtrl.loadtaskcount();
    });
    return null;
  }

  @override
  Future<void>? initState() {
    super.initState();
    loadMetadata();
    loadPendingTasksdata();
    return null;

    //   _pageController = PageController();
  }

  @override
  void dispose() {
    //   _pageController.dispose();
    super.dispose();
  }

  onChangedFunction(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  /* nextFunction() {
    _pageController.nextPage(duration: _kDuration, curve: _kCurve);
  }

  previousFunction() {
    _pageController.previousPage(duration: _kDuration, curve: _kCurve);
  }*/

  @override
  Widget build(BuildContext context) {
    bool isLargeScreen = CommonUtils.isTablet(context);
    // bool isWeb = GetPlatform.isWeb;
    int cac = iconsizesetup();
    return Scaffold(
      drawer: homedrawer(),
      appBar: homeappbar() as PreferredSizeWidget?,
      bottomNavigationBar: homebottombar(),
      body: SafeArea(
        child: Stack(children: <Widget>[
          Positioned(
            top: 00,
            right: 10,
            left: 10,
            bottom: 0,
            child: Stack(children: <Widget>[
              PageView(
                allowImplicitScrolling: true,
                onPageChanged: onChangedFunction,
                controller: _pageController,
                children: <Widget>[
                  ClipRRect(
                    child: Obx(
                      () => talabel.labelsloading.value
                          ? const ProgressIndicatorCust()
                          : Container(
                              padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                              // color: Colors.white,
                              child: SingleChildScrollView(
                                physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                                child: Column(
                                  children: [
                                    if (talabel.get('TMCMOBILE_HOME_MAP') != null ||
                                        (talabel.get('TMCMOBILE_HOME_ENTITYLOCATE') != null) ||
                                        talabel.get('TMCMOBILE_HOME_TARGETS') != null ||
                                        talabel.get('TMCMOBILE_HOME_SITES') != null)
                                      SectionTile(
                                        child: GridView.count(
                                          physics: const NeverScrollableScrollPhysics(),
                                          crossAxisCount: cac,
                                          childAspectRatio: 1.0,
                                          //padding: const EdgeInsets.all(10.0),
                                          mainAxisSpacing: 8,
                                          crossAxisSpacing: 8,
                                          shrinkWrap: true,
                                          children: <Widget>[
                                            if (talabel.get('TMCMOBILE_HOME_MAP') != null)
                                              GestureDetector(
                                                onTap: () => ComponentUtils.mapviewer(context),
                                                child: HomeIcon(
                                                  iconName: 'map',
                                                  label: talabel.get('TMCMOBILE_HOME_MAP'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_ENTITYLOCATE') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => Locate()),
                                                child: HomeIcon(
                                                  iconName: 'locate',
                                                  label: talabel.get('TMCMOBILE_HOME_ENTITYLOCATE'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_TARGETS') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => TargetSearch()),
                                                child: HomeIcon(
                                                  iconName: 'target',
                                                  label: talabel.get('TMCMOBILE_HOME_TARGETS'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_SITES') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => SiteSearch()),
                                                child: HomeIcon(
                                                  iconName: 'site',
                                                  label: talabel.get('TMCMOBILE_HOME_SITES'),
                                                ),
                                              ),
                                            // if (talabel.get('TMCMOBILE_HOME_SITES') != null)
                                            //   new GestureDetector(
                                            //     onTap: () => Get.to(() => SiteSearch()),
                                            //     child: HomeIcon(
                                            //       iconName: 'survey',
                                            //       label: talabel.get('TMCMOBILE_HOME_SITES'),
                                            //     ),
                                            //   ),
                                          ],
                                        ),
                                      ),
                                    if (talabel.get('TMCMOBILE_HOME_PROJECTS') != null ||
                                        talabel.get('TMCMOBILE_HOME_BIDDING') != null ||
                                        talabel.get('TMCMOBILE_HOME_SUPPLIERMGMT') != null ||
                                        talabel.get('TMCMOBILE_HOME_PROGRAMS') != null)
                                      SectionTile(
                                        child: GridView.count(
                                          physics: const NeverScrollableScrollPhysics(),
                                          crossAxisCount: cac,
                                          childAspectRatio: 1.0,
                                          //padding: const EdgeInsets.all(10.0),
                                          mainAxisSpacing: 8,
                                          crossAxisSpacing: 8,
                                          shrinkWrap: true,
                                          children: <Widget>[
                                            if (talabel.get('TMCMOBILE_HOME_PROGRAMS') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => ProgramSearch()),
                                                child: HomeIcon(
                                                  iconName: 'programs',
                                                  label: talabel.get('TMCMOBILE_HOME_PROGRAMS'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_PROJECTS') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => ProjectSearch()),
                                                child: HomeIcon(
                                                  iconName: 'project',
                                                  label: talabel.get('TMCMOBILE_HOME_PROJECTS'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_BIDDING') != null)
                                              GestureDetector(
                                                onTap: () => ComponentUtils.biddingviewer(
                                                    context, talabel.get('TMCMOBILE_HOME_BIDDING')?.value ?? 'Bidding'),
                                                child: HomeIcon(
                                                  iconName: 'bidding',
                                                  label: talabel.get('TMCMOBILE_HOME_BIDDING'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_SUPPLIERMGMT') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => SupplierManagementSearch()),
                                                child: HomeIcon(
                                                  iconName: 'suppliernew',
                                                  label: talabel.get('TMCMOBILE_HOME_SUPPLIERMGMT'),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    if (talabel.get('TMCMOBILE_HOME_FIND') != null ||
                                        talabel.get('TMCMOBILE_HOME_LOCATIONS') != null ||
                                        talabel.get('TMCMOBILE_HOME_PROPERTIES') != null ||
                                        talabel.get('TMCMOBILE_HOME_BUILDINGS') != null ||
                                        talabel.get('TMCMOBILE_HOME_RESERVATION') != null ||
                                        talabel.get('TMCMOBILE_HOME_MAC') != null ||
                                        talabel.get('TMCMOBILE_HOME_SERVICEREQUEST') != null)
                                      SectionTile(
                                        child: GridView.count(
                                          physics: const NeverScrollableScrollPhysics(),
                                          crossAxisCount: cac,
                                          childAspectRatio: 1.0,
                                          //padding: const EdgeInsets.all(10.0),
                                          mainAxisSpacing: 8,
                                          crossAxisSpacing: 8,
                                          shrinkWrap: true,
                                          children: <Widget>[
                                            if (talabel.get('TMCMOBILE_HOME_LOCATIONS') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => LocationEntitySearch(
                                                      entitytype: 'STORE',
                                                    )),
                                                child: HomeIcon(
                                                  iconName: 'locations',
                                                  label: talabel.get('TMCMOBILE_HOME_LOCATIONS'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_PROPERTIES') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(
                                                  () => PropertySearch(),
                                                ),
                                                child: HomeIcon(
                                                  iconName: 'locations',
                                                  label: talabel.get('TMCMOBILE_HOME_PROPERTIES'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_BUILDINGS') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(
                                                  () => BuildingsSearch(),
                                                ),
                                                child: HomeIcon(
                                                  iconName: 'locations',
                                                  label: talabel.get('TMCMOBILE_HOME_BUILDINGS'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_FIND') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => Find()),
                                                child: HomeIcon(
                                                  iconName: 'find',
                                                  label: talabel.get('TMCMOBILE_HOME_FIND'),
                                                ),
                                              ),
                                            // if (talabel.get('TMCMOBILE_HOME_FIND') != null)
                                            //   GestureDetector(
                                            //     onTap: () => Navigator.pushNamed(context, FindScreen.routName),
                                            //     child: HomeIcon(
                                            //       iconName: 'find',
                                            //       label: talabel.get('TMCMOBILE_HOME_FIND'),
                                            //     ),
                                            //   ),
                                            if (talabel.get('TMCMOBILE_HOME_RESERVATION') != null)
                                              GestureDetector(
                                                onTap: () => Navigator.pushNamed(context, ReservationsScreen.routName),
                                                child: HomeIcon(
                                                  iconName: 'reservations',
                                                  label: talabel.get('TMCMOBILE_HOME_RESERVATION'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_RESERVATION1') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => Reservations()),
                                                child: HomeIcon(
                                                  iconName: 'reservations',
                                                  label: talabel.get('TMCMOBILE_HOME_RESERVATION'),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_SERVICEREQUEST1') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => const DynamicWidgetPage()),
                                                child: HomeIcon(
                                                  iconName: 'reservations',
                                                  label: talabel.get('TMCMOBILE_HOME_SERVICEREQUEST'),
                                                ),
                                              ),

                                            // if (talabel.get('TMCMOBILE_HOME_MAC') != null)
                                            //   new GestureDetector(
                                            //     onTap: () => Navigator.pushNamed(context, MacRequestSearchScreen.routName),
                                            //     child: HomeIcon(
                                            //       iconName: 'new_MAC_requests',
                                            //       label: talabel.get('TMCMOBILE_HOME_MAC'),
                                            //     ),
                                            //   ),
                                            // new GestureDetector(
                                            //   onTap: () => Get.to(() => MacRequestSearch()),
                                            //   child: HomeIcon(
                                            //     iconName: 'new_MAC_requests',
                                            //     label: new Applabel(value: 'Mac Request'),
                                            //   ),
                                            // ),
                                            // if (talabel.get('TMCMOBILE_HOME_SERVICEREQUEST') != null)
                                            //   new GestureDetector(
                                            //     onTap: () => Navigator.pushNamed(context, ServiceRequestsScreen.routName),
                                            //     child: HomeIcon(
                                            //       iconName: 'servicereq',
                                            //       label: talabel.get('TMCMOBILE_HOME_SERVICEREQUEST'),
                                            //     ),
                                            //   ),

                                            if (talabel.get('TMCMOBILE_HOME_SERVICEREQUEST') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => ServiceRequests()),
                                                child: HomeIcon(
                                                  iconName: 'servicereq',
                                                  label: talabel.get('TMCMOBILE_HOME_SERVICEREQUEST'),
                                                ),
                                              ),

                                            // if (AppResourceBundleImpl().isVisible('TMCMOBILE_HOME_WAYFINDING'))
                                            //   new GestureDetector(
                                            //       child: HomeWidget(
                                            //     componentKey: 'TMCMOBILE_HOME_WAYFINDING',
                                            //     iconName: 'way_finding',
                                            //     fontSize: 12,
                                            //   )),
                                          ],
                                        ),
                                      ),
                                    if (talabel.get('TMCMOBILE_HOME_LOCATIONS1') != null)
                                      SectionTile(
                                        child: GridView.count(
                                          physics: const NeverScrollableScrollPhysics(),
                                          crossAxisCount: cac,
                                          childAspectRatio: 1.0,
                                          //padding: const EdgeInsets.all(10.0),
                                          mainAxisSpacing: 8,
                                          crossAxisSpacing: 8,
                                          shrinkWrap: true,
                                          children: <Widget>[
                                            if (talabel.get('TMCMOBILE_HOME_LEASEHOME') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => LeaseHome()),
                                                child: Obx(
                                                  () => HomeIcon(
                                                    iconName: 'lease',
                                                    label: talabel.get('TMCMOBILE_HOME_LEASEHOME'),
                                                  ),
                                                ),
                                              ),
                                            //),
                                          ],
                                        ),
                                      ),
                                    if (talabel.get('TMCMOBILE_HOME_APPROVALS') != null)
                                      SectionTile(
                                        child: GridView.count(
                                          physics: const NeverScrollableScrollPhysics(),
                                          crossAxisCount: cac,
                                          childAspectRatio: 1.0,
                                          //padding: const EdgeInsets.all(10.0),
                                          mainAxisSpacing: 8,
                                          crossAxisSpacing: 8,
                                          shrinkWrap: true,
                                          children: <Widget>[
                                            if (talabel.get('TMCMOBILE_HOME_APPROVALS') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => Myapprovals()),
                                                child: Obx(
                                                  () => HomeIcon(
                                                    count: homeCtrl.wfcnt.value.toString(),
                                                    iconName: 'approvals',
                                                    label: talabel.get('TMCMOBILE_HOME_APPROVALS'),
                                                  ),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_APPROVALS1') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => MyTasks()),
                                                child: Obx(
                                                  () => HomeIcon(
                                                    count: homeCtrl.wfcnt.value.toString(),
                                                    iconName: 'approvals',
                                                    label: talabel.get('TMCMOBILE_HOME_APPROVALS'),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    if (talabel.get('TMCMOBILE_HOME_AQMENU') != null &&
                                        homeCtrl.aqserverurl.value != '')
                                      SectionTile(
                                        // containerhdr: "Agile Quest",
                                        child: GridView.count(
                                          physics: const NeverScrollableScrollPhysics(),
                                          crossAxisCount: cac,
                                          childAspectRatio: 1.0,
                                          //padding: const EdgeInsets.all(10.0),
                                          mainAxisSpacing: 8,
                                          crossAxisSpacing: 8,
                                          shrinkWrap: true,
                                          children: <Widget>[
                                            if (talabel.get('TMCMOBILE_HOME_AQMENU') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => AqLoginScreen()),
                                                child: Obx(
                                                  () => HomeIcon(
                                                    iconName: 'lease',
                                                    label: talabel.get('TMCMOBILE_HOME_AQMENU'),
                                                  ),
                                                ),
                                              ),
                                            if (talabel.get('TMCMOBILE_HOME_AQMENU1') != null)
                                              GestureDetector(
                                                onTap: () => Get.to(() => AqFloorPlan()),
                                                child: Obx(
                                                  () => HomeIcon(
                                                    iconName: 'lease',
                                                    label: talabel.get('TMCMOBILE_HOME_AQMENU'),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ]),
          ),
        ]),
      ),
    );
  }

  int iconsizesetup() {
    bool isWeb = GetPlatform.isWeb;
    bool isLargeScreen = CommonUtils.isTablet(context);
    sWidth = Get.width;
    //debugPrint('screenwidth>>>>> $sWidth');
    return isLargeScreen
        ? isWeb
            ? (sWidth < 900.0
                ? 5
                : sWidth < 1000
                    ? 6
                    : sWidth < 1100
                        ? 7
                        : sWidth < 1200
                            ? 8
                            : sWidth < 1300
                                ? 9
                                : 10)
            : 5
        : (sWidth > 350 ? 3 : 2);
  }

  Widget homeappbar() {
    bool isLargeScreen = CommonUtils.isTablet(context);
    return AppBar(
        iconTheme: Theme.of(context).appBarTheme.iconTheme,
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(
              Icons.menu,
              color: Theme.of(context).primaryColor,
            ),
            onPressed: () => Scaffold.of(context).openDrawer(),
          ),
        ),
        actions: <Widget>[
          Padding(
              padding: const EdgeInsets.only(right: 20.0),
              child: TextButton(
                onPressed: () {
                  isLargeScreen ? showChangeContextDialog(context) : Navigator.pushNamed(context, TaChange.routName);
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.settings_backup_restore_rounded,
                      size: isLargeScreen ? 22 : 18,
                      //color: Colors.white,
                    ),
                    const Text(
                      'country',
                      //style: ComponentUtils.appbartitlestyle,
                      //TextStyle(fontSize: isLargeScreen ? 15 : 11, fontWeight: FontWeight.normal, color: Colors.white
                    ),
                  ],
                ),
              )),
        ],
        title: Text('${SharedPrefUtils.readPrefStr(ConstHelper.userNamevar)}', style: ComponentUtils.appbartitlestyle),
        // style: TextStyle(fontSize: isLargeScreen ? 16 : 12, fontWeight: FontWeight.bold)),
        // toolbarHeight: 50,
        backgroundColor: Colors.white,
        elevation: 5);
  }

  Future<void> showChangeContextDialog(BuildContext context) {
    return showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            content: TaChange(),
          );
        });
  }

  Widget homedrawer() {
    bool isLargeScreen = CommonUtils.isTablet(context);
    return Drawer(
      child: Obx(
        () => ListView(
          // Important: Remove any padding from the ListView.

          padding: EdgeInsets.zero,
          children: <Widget>[
            SizedBox(
              height: 80.0,
              child: DrawerHeader(
                  decoration: const BoxDecoration(color: Colors.grey),
                  margin: EdgeInsets.zero,
                  padding: EdgeInsets.zero,
                  child: new Text('', style: TextStyle(color: Colors.white))),
            ),
            createDrawerBodyItem(
              icon: Icons.settings_backup_restore_rounded,
              text: 'Country',
              onTap: () {
                Navigator.pop(context);
                isLargeScreen ? showChangeContextDialog(context) : Navigator.pushNamed(context, TaChange.routName);
              },
            ),

            if (homeCtrl.hostlist.contains(homeCtrl.env.value.toUpperCase()))
              createDrawerBodyItem(
                icon: Icons.cached,
                text: 'Refresh',
                onTap: () async {
                  await CommonUtils.refresh(context);
                },
              ),
            // Divider(),
            // createDrawerBodyItem(icon: Icons.settings, text: 'Settings', onTap: () => Navigator.pushNamed(context, SettingsForm.routName)),
            const Divider(),
            ListTile(
                title: const Row(
                  children: <Widget>[
                    Icon(Icons.logout),
                    Padding(
                      padding: EdgeInsets.only(left: 8.0),
                      child: Text('Log out'),
                    )
                  ],
                ),
                onTap: () {
                  // AppResourceBundleImpl().clear();
                  // _pageController.dispose();
                  CommonUtils.logout(context);
                }),
            const Divider()
          ],
        ),
      ), // Populate the Drawer in the next step.
    );
  }

  Widget homebottombar() {
    return BottomAppBar(
      elevation: 5,
      //to add a space between the FAB and BottomAppBar
      //color of the BottomAppBar
      color: Colors.white,
      child: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Transform.scale(
              scale: 3,
              child: IconButton(
                //update the bottom app bar view each time an item is clicked
                icon: const Image(
                  image: AssetImage('lib/icons/talogo_new.png'),
                ),
                onPressed: () {},
              ),
            ),
            const SizedBox(
              width: 20.0,
            ),
            const Text("© 2025", style: TextStyle(fontSize: 12))
          ],
        ),
      ),
    );
  }
}

class HomeIcon extends StatelessWidget {
  final iconName;
  final Applabel? label;
  String? count;

  HomeIcon({
    super.key,
    this.iconName,
    this.label,
    this.count,
  });
  HomeController homeCtrl = Get.isRegistered<HomeController>() ? Get.find<HomeController>() : HomeController();

  //homeCtrl = Get.find<HomeController>();

  @override
  Widget build(BuildContext context) {
    bool isLargeScreen = CommonUtils.isTablet(context);

    return isLargeScreen
        ? SizedBox(
            //margin: EdgeInsets.fromLTRB(1, 1, 1, 1),
            //color: Colors.black38,
            height: double.infinity,
            width: double.infinity,

            child: Stack(
              alignment: AlignmentDirectional.bottomCenter,
              children: [
                Positioned.fill(
                    top: isLargeScreen ? -40 : -50,
                    //bottom: -10,
                    child: FittedBox(
                      fit: BoxFit.fitWidth,
                      child: Image(
                        image: AssetImage('lib/icons/$iconName.png'),
                        width: 250,
                        height: 250,

                        // fit: BoxFit.cover,
                      ),
                    )),
                Row(
                  //crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Container(
                        //color: Colors.blueAccent,
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 30),
                        //margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                        child: Text(
                          label!.value ?? '',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: isLargeScreen ? 17 : 12, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            //),
          )
        : Container(
            margin: const EdgeInsets.all(1.0),
            //padding: const EdgeInsets.all(3.0),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.black26),
              borderRadius: BorderRadius.circular(8.0),
            ),
            height: double.infinity,
            width: double.infinity,
            // color: Colors.black12,
            child: Stack(
              alignment: AlignmentDirectional.bottomCenter,
              children: [
                Positioned.fill(
                  top: -20,
                  //bottom: -10,
                  child: FittedBox(
                    fit: BoxFit.fitWidth,
                    child: Image(
                      image: AssetImage('lib/icons/$iconName.png'),
                      width: 170,
                      height: 170,

                      // fit: BoxFit.cover,
                    ),
                  ),
                ),
                Row(
                  //crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Container(
                        //color: Colors.blueAccent,
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 8),
                        //margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
                        child: iconName == 'approvals' && homeCtrl.wfcntflag.isTrue
                            ? Text(
                                count != null ? '${label!.value!}-${count ?? ''}' : label!.value!,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                                style: TextStyle(fontSize: isLargeScreen ? 22 : 12, fontWeight: FontWeight.bold),
                              )
                            : Text(
                                label!.value ?? '',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                                style: TextStyle(fontSize: isLargeScreen ? 22 : 12, fontWeight: FontWeight.bold),
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            //),
          );
  }
}
