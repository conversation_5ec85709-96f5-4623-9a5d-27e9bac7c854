//import 'dart:html';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/screens/mytasks/usertasks/scheduledtasks_widget.dart';

import '../../../common/component_utils.dart';
import '../../../providers/ta_admin/label_controller.dart';
import '../../../utils/common_utils.dart';
import 'notificationtasks_widget.dart';

class MyTasks extends StatelessWidget {
  MyTasks({
    super.key,
  });
  LabelController talabel = Get.find<LabelController>();

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  TabController? _tabController;
  //MyApprovalsController myApprovalsCntrlr = Get.find<MyApprovalsController>();

  final primary = ComponentUtils.primary;

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: 0,
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          iconTheme: Theme.of(context).appBarTheme.iconTheme,
          title: Text('My Tasks', style: ComponentUtils.appbartitlestyle //Theme.of(context).appBarTheme.titleTextStyle,
              ),
          backgroundColor: Colors.white,
          elevation: 5,
          leading: IconButton(
            icon: ComponentUtils.backpageIcon,
            color: CommonUtils.createMaterialColor(const Color(0XFFb10c00)),
            onPressed: () async {
              debugPrint('------back-------');

              Get.back();
            },
          ),
          bottom: TabBar(
            isScrollable: true,
            labelColor: ComponentUtils.tablabelcolor,
            unselectedLabelColor: ComponentUtils.tabunselectedLabelColor,
            indicatorColor: ComponentUtils.tabindicatorColor,
            indicatorSize: TabBarIndicatorSize.tab,
            labelStyle: TextStyle(
                fontWeight: FontWeight.bold,
                fontStyle: FontStyle.normal,
                fontSize: 14,
                color: CommonUtils.createMaterialColor(const Color(0XFF3953A4))), // HexColor('#3953A4')),
            tabs: [
              const Tab(
                text: 'Scheduled Tasks',
              ),
              const Tab(
                text: 'Adhoc Tasks',
              ),
              const Tab(
                text: 'Notifications',
              ),
            ],
          ),
        ),
        //],),

        key: _scaffoldKey,
        body:
            // GetX<LabelController>(
            //   initState: (state) {
            //     Future.delayed(const Duration(seconds: 1), () async {
            //       // await _getlabels(talabel);
            //     });
            //   },
            //   builder: (_) {
            //return
            TabBarView(
          children: <Widget>[
            ScheduledtasksWidget(),
            Container(),
            NotificationTaskWidget(),
          ],
        ),
        // },
      ),
      //  ),
    );
  }
}
