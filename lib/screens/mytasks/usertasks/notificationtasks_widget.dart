import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../models/mytasks/usertasks/notificationtask.dart';
import '../../../providers/mytasks/usertasks/notificationtasks_controller.dart';

class NotificationTaskWidget extends StatelessWidget {
  NotificationTaskWidget({
    super.key,
  });
  LabelController talabel = Get.find<LabelController>();

  NotificationTasksController notificationTasksController = Get.put(NotificationTasksController());
  NotificationTasksController notiCtrlr = Get.find<NotificationTasksController>();

  Future _refreshlist() async {
    await notiCtrlr.fetchNotificationTasks();
  }

  @override
  Widget build(BuildContext context) {
    return listStack(context);
  }

  Widget listStack(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      Positioned.fill(
        //top: adhocTaskCtrl.isContractor.value == 'N' ? 45 : 1,
        child: RefreshIndicator(
          onRefresh: _refreshlist,
          child: GetX<NotificationTasksController>(
            initState: (state) async {
              await notiCtrlr.fetchNotificationTasks();
            },
            builder: (_) {
              return _.isloading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.notificationtasksrecords.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.notificationtasksrecords[index]);
                          },
                          itemCount: _.notificationtasksrecords.length,
                        );
            },
          ),
        ),
      ),
    ]);
  }

  Widget listitem(BuildContext context, NotificationTaskRecords s) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;
    final labelStyle = TextStyle(
      color: primary,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );

    return GestureDetector(
      onTap: () async {
        // Get.to(() => SiteDetails(
        //       nav_from: 'target_associted_sites',
        //       siteid: s.siteId,
        //       sitename: s.siteName,
        //     ));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              //if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID') != null)
              // ComponentUtils.listVertRow(
              //     talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),

              ComponentUtils.listVertRow('Entity Type', s.entityType ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Entity Name', s.entityName ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Entity Id', s.entityId?.toString() ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Entity Number', s.entityNumber ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Message', s.message ?? '', labelStyle: labelStyle),
              //talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Completion Date', s.completionDate ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Creation Date', s.creationDate ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
            ],
          ),
        ),
      ),
    );
  }
}
