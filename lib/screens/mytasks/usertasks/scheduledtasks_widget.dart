import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:tangoworkplace/common/progess_indicator_cust.dart';
import 'package:tangoworkplace/providers/ta_admin/label_controller.dart';
import 'package:tangoworkplace/utils/device_util.dart';

import '../../../common/component_utils.dart';
import '../../../models/mytasks/usertasks/notificationtask.dart';
import '../../../models/mytasks/usertasks/scheduledtask.dart';
import '../../../providers/mytasks/usertasks/notificationtasks_controller.dart';
import '../../../providers/mytasks/usertasks/scheduledtasks_controller.dart';

class ScheduledtasksWidget extends StatelessWidget {
  ScheduledtasksWidget({
    super.key,
  });
  LabelController talabel = Get.find<LabelController>();

  ScheduledtasksController scheduledTasksController = Get.put(ScheduledtasksController());
  ScheduledtasksController schdCtrlr = Get.find<ScheduledtasksController>();

  Future _refreshlist() async {
    await schdCtrlr.fetchScheduledTasks();
  }

  @override
  Widget build(BuildContext context) {
    return listStack(context);
  }

  Widget listStack(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: <Widget>[
      Positioned.fill(
        //top: adhocTaskCtrl.isContractor.value == 'N' ? 45 : 1,
        child: RefreshIndicator(
          onRefresh: _refreshlist,
          child: GetX<ScheduledtasksController>(
            initState: (state) async {
              await schdCtrlr.fetchScheduledTasks();
            },
            builder: (_) {
              return _.isloading.isTrue
                  ? const ProgressIndicatorCust()
                  : _.scheduledtasksrecords.isEmpty
                      ? Center(child: Text('No Data', style: TextStyle(fontSize: DeviceUtils.taFontSize(1.5, context))))
                      : ListView.builder(
                          itemBuilder: (context, index) {
                            return listitem(context, _.scheduledtasksrecords[index]);
                          },
                          itemCount: _.scheduledtasksrecords.length,
                        );
            },
          ),
        ),
      ),
    ]);
  }

  Widget listitem(BuildContext context, ScheduledTaskRecords s) {
    final primary = ComponentUtils.primary;
    final secondary = ComponentUtils.secondary;
    final labelStyle = TextStyle(
      color: primary,
      fontSize: 12,
      fontWeight: FontWeight.bold,
    );

    return GestureDetector(
      onTap: () async {
        // Get.to(() => SiteDetails(
        //       nav_from: 'target_associted_sites',
        //       siteid: s.siteId,
        //       sitename: s.siteName,
        //     ));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
        ),
        width: double.infinity,
        //height: 110,
        margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        //padding: EdgeInsets.only(right: 10, left: 10, top: 5, bottom: 5),
        child: Container(
          padding: const EdgeInsets.only(left: 5, top: 5, bottom: 5),
          margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              //if (talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID') != null)
              // ComponentUtils.listVertRow(
              //     talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),

              ComponentUtils.listVertRow('Entity Type', s.entityType ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Entity Name', s.entityName ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Entity Id', s.entityId?.toString() ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Milestone', s.milestoneName ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Description', s.description ?? '', labelStyle: labelStyle),
              //talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Baseline Start', s.baseLineStart ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Baseline End', s.baseLineEnd ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Forecast Start', s.forecastStart ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Forecast End', s.forecastEnd ?? '', labelStyle: labelStyle),
              //talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Actual Start', s.actualStart ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
              ComponentUtils.listVertRow('Actual End', s.actualFinish ?? '', labelStyle: labelStyle),
              // talabel.get('TMCMOBILE_PROJECTS_BUDGET_PO_POID')!.value!, po.poId.toString() ?? '0'),
            ],
          ),
        ),
      ),
    );
  }
}
