import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/mytasks/usertasks/scheduledtask.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';
import '../../../utils/user_secure_storage.dart';

class ScheduledtasksController extends GetxController {
  var isloading = true.obs;
  RxList<ScheduledTaskRecords> scheduledtasksrecords = <ScheduledTaskRecords>[].obs;

  @override
  void onInit() {
    super.onInit();
    // fetchScheduledTasks();
  }

  Future<void> fetchScheduledTasks() async {
    debugPrint('Fetching scheduled tasks...');
    scheduledtasksrecords.clear();
    isloading.value = true;

    try {
      final userName = UserContext.info()?.userName;

      final filter = {
        'a': ['p_user_name', 'EXACT', userName],
      };
      final payload = CommonServices.getFilter(filter, size: 500);
      final resMap = await CommonServices.fetchDynamicApi(
        scheduledtasksurl,
        payloadObj: payload,
      );

      if (resMap == null) {
        debugPrint('No response received from API.');
        scheduledtasksrecords.clear();
        return;
      }

      final status = resMap['status'] as int?;
      if (status != 0) {
        debugPrint('API returned non-zero status: $status');
        scheduledtasksrecords.clear();
        return;
      }

      final records = resMap['records'] as List?;
      if (records == null) {
        debugPrint('No records found in API response.');
        scheduledtasksrecords.clear();
        return;
      }

      scheduledtasksrecords.value = records.map((item) => ScheduledTaskRecords.fromJson(item)).toList();
    } catch (e, stackTrace) {
      debugPrint('Error fetching scheduled tasks: $e');
      debugPrint(stackTrace.toString());
      scheduledtasksrecords.clear();
    } finally {
      isloading.value = false;
    }
  }
}
