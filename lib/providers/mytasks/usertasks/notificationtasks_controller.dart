import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/mytasks/usertasks/notificationtask.dart';
import '../../../services/common_services.dart';
import '../../../utils/connections.dart';
import '../../../utils/user_secure_storage.dart';

class NotificationTasksController extends GetxController {
  var isloading = true.obs;
  RxList<NotificationTaskRecords> notificationtasksrecords = <NotificationTaskRecords>[].obs;

  @override
  void onInit() {
    super.onInit();
    // fetchScheduledTasks();
  }

  Future<void> fetchNotificationTasks() async {
    debugPrint('Fetching notification tasks...');
    notificationtasksrecords.clear();
    isloading.value = true;

    try {
      final userName = UserContext.info()?.userName;

      final filter = {
        'a': ['assigned_to', 'EXACT', userName],
      };
      final payload = CommonServices.getFilter(filter, size: 500);
      final resMap = await CommonServices.fetchDynamicApi(
        notificationtasksurl,
        payloadObj: payload,
      );

      if (resMap == null) {
        debugPrint('No response received from API.');
        notificationtasksrecords.clear();
        return;
      }

      final status = resMap['status'] as int?;
      if (status != 0) {
        debugPrint('API returned non-zero status: $status');
        notificationtasksrecords.clear();
        return;
      }

      final records = resMap['records'] as List?;
      if (records == null) {
        debugPrint('No records found in API response.');
        notificationtasksrecords.clear();
        return;
      }

      notificationtasksrecords.value = records.map((item) => NotificationTaskRecords.fromJson(item)).toList();
    } catch (e, stackTrace) {
      debugPrint('Error fetching notification tasks: $e');
      debugPrint(stackTrace.toString());
      notificationtasksrecords.clear();
    } finally {
      isloading.value = false;
    }
  }
}
